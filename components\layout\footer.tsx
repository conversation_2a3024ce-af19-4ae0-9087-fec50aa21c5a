import Link from "next/link"
import { School, Phone, Mail, MapPin, Facebook, Twitter, Instagram, Linkedin, Youtube } from "lucide-react"
import { Button } from "@/components/ui/button"

export function Footer() {
  return (
    <footer className="bg-muted/50 border-t">
      <div className="container px-4 md:px-6 py-12 md:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <School className="h-6 w-6" />
              <span className="font-bold text-xl">NES High School</span>
            </div>
            <p className="text-muted-foreground text-sm">
              Providing exceptional education with a focus on academic excellence, character development, and community engagement since 1975.
            </p>
            <div className="flex space-x-3">
              <Button variant="ghost" size="icon" asChild aria-label="Facebook">
                <Link href="#"><Facebook className="h-4 w-4" /></Link>
              </Button>
              <Button variant="ghost" size="icon" asChild aria-label="Twitter">
                <Link href="#"><Twitter className="h-4 w-4" /></Link>
              </Button>
              <Button variant="ghost" size="icon" asChild aria-label="Instagram">
                <Link href="#"><Instagram className="h-4 w-4" /></Link>
              </Button>
              <Button variant="ghost" size="icon" asChild aria-label="LinkedIn">
                <Link href="#"><Linkedin className="h-4 w-4" /></Link>
              </Button>
              <Button variant="ghost" size="icon" asChild aria-label="YouTube">
                <Link href="#"><Youtube className="h-4 w-4" /></Link>
              </Button>
            </div>
          </div>
          
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about/school" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/academics/curriculum" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Academics
                </Link>
              </li>
              <li>
                <Link href="/admissions/apply" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Admissions
                </Link>
              </li>
              <li>
                <Link href="/campus-life/activities" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Campus Life
                </Link>
              </li>
              <li>
                <Link href="/alumni/directory" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Alumni
                </Link>
              </li>
              <li>
                <Link href="/contact/form" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Resources</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/academics/question-bank" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Question Bank
                </Link>
              </li>
              <li>
                <Link href="/news" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  School News
                </Link>
              </li>
              <li>
                <Link href="/calendar" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Event Calendar
                </Link>
              </li>
              <li>
                <Link href="/careers" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Career Opportunities
                </Link>
              </li>
              <li>
                <Link href="/policies" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  School Policies
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
                  Photo Gallery
                </Link>
              </li>
            </ul>
          </div>
          
          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Contact Us</h3>
            <ul className="space-y-3">
              <li className="flex items-start space-x-2">
                <MapPin className="h-4 w-4 mt-1 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">123 Education Blvd, Academic City, AC 12345</span>
              </li>
              <li className="flex items-center space-x-2">
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">(*************</span>
              </li>
              <li className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground"><EMAIL></span>
              </li>
            </ul>
            <div className="pt-2">
              <Button asChild>
                <Link href="/contact/form">Contact Us</Link>
              </Button>
            </div>
          </div>
        </div>
        
        <div className="border-t mt-10 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-xs text-muted-foreground">© {new Date().getFullYear()} NES High School. All rights reserved.</p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <Link href="/privacy" className="text-xs text-muted-foreground hover:text-foreground transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms" className="text-xs text-muted-foreground hover:text-foreground transition-colors">
              Terms of Use
            </Link>
            <Link href="/accessibility" className="text-xs text-muted-foreground hover:text-foreground transition-colors">
              Accessibility
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}