import Link from "next/link";
import {
  CalendarDays,
  Map,
  GraduationCap,
  BookOpen,
  Users,
  Image,
  ArrowRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { HeroSection } from "@/components/home/<USER>";
import { FeatureCard } from "@/components/home/<USER>";
import { NewsCard } from "@/components/home/<USER>";
import { StatsSection } from "@/components/home/<USER>";
import { TestimonialSection } from "@/components/home/<USER>";
import { CtaSection } from "@/components/home/<USER>";

export default function Home() {
  return (
    <main className="flex flex-col items-center justify-between">
      <HeroSection />

      {/* Features Section */}
      <section className="w-full section-padding bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-950 dark:to-gray-900/50">
        <div className="container">
          <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
            <div className="space-y-4">
              <div className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/20 px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-400">
                <GraduationCap className="mr-2 h-4 w-4" />
                Excellence in Education
              </div>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-balance">
                Why Choose NES High School?
              </h2>
              <p className="max-w-[800px] text-lg text-muted-foreground leading-relaxed">
                We provide exceptional education with a focus on holistic
                development and academic excellence, preparing students for
                success in college and beyond.
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            <FeatureCard
              icon={<GraduationCap className="h-10 w-10 text-primary" />}
              title="Academic Excellence"
              description="Rigorous curriculum designed to challenge and inspire students to reach their full potential."
            />
            <FeatureCard
              icon={<Users className="h-10 w-10 text-primary" />}
              title="Dedicated Faculty"
              description="Experienced teachers committed to student success through personalized attention."
            />
            <FeatureCard
              icon={<BookOpen className="h-10 w-10 text-primary" />}
              title="Comprehensive Learning"
              description="Diverse programs that develop critical thinking and practical skills for future success."
            />
            <FeatureCard
              icon={<Image className="h-10 w-10 text-primary" />}
              title="Modern Facilities"
              description="State-of-the-art campus with specialized labs, libraries, and recreational spaces."
            />
            <FeatureCard
              icon={<CalendarDays className="h-10 w-10 text-primary" />}
              title="Enriching Activities"
              description="Wide range of extracurricular activities to foster creativity, teamwork, and leadership."
            />
            <FeatureCard
              icon={<Map className="h-10 w-10 text-primary" />}
              title="Community Engagement"
              description="Opportunities for students to contribute to society through meaningful service initiatives."
            />
          </div>
        </div>
      </section>

      {/* News & Announcements */}
      <section className="w-full section-padding bg-gradient-to-br from-muted/30 to-muted/10">
        <div className="container">
          <div className="flex flex-col items-start justify-between gap-6 md:flex-row md:items-center mb-12">
            <div className="space-y-4">
              <div className="inline-flex items-center rounded-full bg-green-100 dark:bg-green-900/20 px-4 py-2 text-sm font-medium text-green-700 dark:text-green-400">
                <CalendarDays className="mr-2 h-4 w-4" />
                Latest Updates
              </div>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-balance">
                News & Announcements
              </h2>
              <p className="max-w-[600px] text-lg text-muted-foreground leading-relaxed">
                Stay updated with the latest happenings, achievements, and
                events at NES High School.
              </p>
            </div>
            <Link
              href="/news"
              className="group inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors duration-200"
            >
              View all news
              <ArrowRight className="h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
            </Link>
          </div>
          <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            <NewsCard
              date="May 15, 2025"
              title="Annual Science Fair Winners Announced"
              description="Congratulations to all participants and winners of this year's Science Fair. The projects demonstrated exceptional creativity and scientific inquiry."
              category="Academic"
            />
            <NewsCard
              date="May 10, 2025"
              title="Sports Day Schedule Released"
              description="The annual Sports Day will be held on June 5th. Check out the schedule of events and make sure to register by May 25th."
              category="Events"
            />
            <NewsCard
              date="May 3, 2025"
              title="College Counseling Sessions Begin Next Week"
              description="Seniors are encouraged to sign up for one-on-one college counseling sessions starting next week to prepare for applications."
              category="Guidance"
            />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Testimonials */}
      <TestimonialSection />

      {/* CTA Section */}
      <CtaSection />
    </main>
  );
}
