import Link from 'next/link'
import { CalendarDays, Map, GraduationCap, BookOpen, Users, Image, ArrowRight } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"
import { HeroSection } from '@/components/home/<USER>'
import { FeatureCard } from '@/components/home/<USER>'
import { NewsCard } from '@/components/home/<USER>'
import { StatsSection } from '@/components/home/<USER>'
import { TestimonialSection } from '@/components/home/<USER>'
import { CtaSection } from '@/components/home/<USER>'

export default function Home() {
  return (
    <main className="flex flex-col items-center justify-between">
      <HeroSection />
      
      {/* Features Section */}
      <section className="w-full py-12 md:py-24 lg:py-32 bg-white dark:bg-gray-950">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-center justify-center space-y-4 text-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">Why Choose NES High School?</h2>
              <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                We provide exceptional education with a focus on holistic development and academic excellence.
              </p>
            </div>
          </div>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
            <FeatureCard 
              icon={<GraduationCap className="h-10 w-10 text-primary" />}
              title="Academic Excellence"
              description="Rigorous curriculum designed to challenge and inspire students to reach their full potential."
            />
            <FeatureCard 
              icon={<Users className="h-10 w-10 text-primary" />}
              title="Dedicated Faculty"
              description="Experienced teachers committed to student success through personalized attention."
            />
            <FeatureCard 
              icon={<BookOpen className="h-10 w-10 text-primary" />}
              title="Comprehensive Learning"
              description="Diverse programs that develop critical thinking and practical skills for future success."
            />
            <FeatureCard 
              icon={<Image className="h-10 w-10 text-primary" />}
              title="Modern Facilities"
              description="State-of-the-art campus with specialized labs, libraries, and recreational spaces."
            />
            <FeatureCard 
              icon={<CalendarDays className="h-10 w-10 text-primary" />}
              title="Enriching Activities"
              description="Wide range of extracurricular activities to foster creativity, teamwork, and leadership."
            />
            <FeatureCard 
              icon={<Map className="h-10 w-10 text-primary" />}
              title="Community Engagement"
              description="Opportunities for students to contribute to society through meaningful service initiatives."
            />
          </div>
        </div>
      </section>

      {/* News & Announcements */}
      <section className="w-full py-12 md:py-24 bg-muted/50">
        <div className="container px-4 md:px-6">
          <div className="flex flex-col items-start justify-between gap-4 md:flex-row md:items-center">
            <div className="space-y-2">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Latest News & Announcements</h2>
              <p className="max-w-[600px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                Stay updated with the latest happenings at NES High School.
              </p>
            </div>
            <Link href="/news" className="inline-flex items-center hover:underline">
              View all news <ArrowRight className="ml-1 h-4 w-4" />
            </Link>
          </div>
          <div className="grid gap-6 pt-8 md:grid-cols-2 lg:grid-cols-3">
            <NewsCard 
              date="May 15, 2025"
              title="Annual Science Fair Winners Announced"
              description="Congratulations to all participants and winners of this year's Science Fair. The projects demonstrated exceptional creativity and scientific inquiry."
              category="Academic"
            />
            <NewsCard 
              date="May 10, 2025"
              title="Sports Day Schedule Released"
              description="The annual Sports Day will be held on June 5th. Check out the schedule of events and make sure to register by May 25th."
              category="Events"
            />
            <NewsCard 
              date="May 3, 2025"
              title="College Counseling Sessions Begin Next Week"
              description="Seniors are encouraged to sign up for one-on-one college counseling sessions starting next week to prepare for applications."
              category="Guidance"
            />
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <StatsSection />

      {/* Testimonials */}
      <TestimonialSection />

      {/* CTA Section */}
      <CtaSection />
    </main>
  )
}