import { schoolStats } from "@/lib/constants"
import { TrendingUp, Users, BookOpen, Calendar } from "lucide-react"

const statIcons = [
  Calendar, // Established
  TrendingUp, // College Acceptance
  BookOpen, // AP Courses
  Users, // Faculty Members
]

export function StatsSection() {
  return (
    <section className="relative w-full section-padding bg-gradient-to-br from-[#00237C] via-[#003366] to-[#004500] text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Cpath d="M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z"/%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

      {/* Floating Elements */}
      <div className="absolute top-10 right-10 w-24 h-24 bg-yellow-400/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 left-10 w-32 h-32 bg-blue-400/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>

      <div className="container relative z-10 px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
          <div className="space-y-4">
            <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm px-4 py-2 text-sm font-medium">
              <TrendingUp className="mr-2 h-4 w-4" />
              Excellence in Numbers
            </div>
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-balance">
              NES High School by the Numbers
            </h2>
            <p className="max-w-[700px] text-lg text-white/90 leading-relaxed">
              Our commitment to excellence reflected in our achievements and the success of our community.
            </p>
          </div>
        </div>

        <div className="mx-auto grid max-w-6xl grid-cols-2 gap-8 md:grid-cols-4 md:gap-12">
          {schoolStats.map((stat, index) => {
            const IconComponent = statIcons[index]
            return (
              <div
                key={index}
                className="group flex flex-col items-center justify-center text-center space-y-4 animate-fade-in"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="relative">
                  {/* Icon Background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/20 to-orange-400/20 rounded-full blur-xl group-hover:blur-2xl transition-all duration-300"></div>
                  <div className="relative p-4 bg-white/10 backdrop-blur-sm rounded-full group-hover:bg-white/20 transition-all duration-300">
                    <IconComponent className="h-8 w-8 text-yellow-400 group-hover:text-yellow-300 transition-colors duration-300" />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="text-4xl font-bold text-white md:text-5xl lg:text-6xl group-hover:scale-110 transition-transform duration-300">
                    {stat.value}
                  </div>
                  <div className="text-sm text-white/80 md:text-base font-medium tracking-wide uppercase">
                    {stat.label}
                  </div>
                </div>

                {/* Decorative line */}
                <div className="w-12 h-0.5 bg-gradient-to-r from-yellow-400 to-orange-400 group-hover:w-16 transition-all duration-300"></div>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}