import { testimonials } from "@/lib/constants";
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>eader,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Quote, Star, GraduationCap } from "lucide-react";

export function TestimonialSection() {
  return (
    <section className="w-full section-padding bg-gradient-to-br from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-6 text-center mb-16">
          <div className="space-y-4">
            <div className="inline-flex items-center rounded-full bg-blue-100 dark:bg-blue-900/20 px-4 py-2 text-sm font-medium text-blue-700 dark:text-blue-400">
              <GraduationCap className="mr-2 h-4 w-4" />
              Alumni Success Stories
            </div>
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-balance">
              What Our Alumni Say
            </h2>
            <p className="max-w-[700px] text-lg text-muted-foreground leading-relaxed">
              Hear from our graduates about their transformative experience at
              NES High School and how it shaped their future success.
            </p>
          </div>
        </div>

        <div className="mx-auto grid max-w-6xl grid-cols-1 gap-8 md:grid-cols-3 md:gap-8">
          {testimonials.map((testimonial, index) => (
            <Card
              key={index}
              className="group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-2 border-0 bg-white dark:bg-gray-800/50 backdrop-blur-sm"
            >
              {/* Gradient overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              {/* Quote icon background */}
              <div className="absolute top-6 right-6 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                <Quote className="h-16 w-16 text-primary" />
              </div>

              <CardHeader className="pb-4 relative z-10">
                {/* Star rating */}
                <div className="flex gap-1 mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <Quote className="h-8 w-8 text-primary mb-2 group-hover:scale-110 transition-transform duration-300" />
              </CardHeader>

              <CardContent className="pb-6 relative z-10">
                <CardDescription className="text-base italic leading-relaxed text-foreground/80 group-hover:text-foreground transition-colors duration-200">
                  "{testimonial.quote}"
                </CardDescription>
              </CardContent>

              <CardFooter className="flex items-center space-x-4 relative z-10">
                <div className="relative">
                  <Avatar className="ring-2 ring-primary/20 group-hover:ring-primary/40 transition-all duration-300">
                    <AvatarImage
                      src={testimonial.avatar}
                      alt={testimonial.author}
                    />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white">
                      {testimonial.author
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  {/* Success indicator */}
                  <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                    <GraduationCap className="h-3 w-3 text-white" />
                  </div>
                </div>
                <div className="flex-1">
                  <p className="text-sm font-semibold text-foreground group-hover:text-primary transition-colors duration-200">
                    {testimonial.author}
                  </p>
                  <p className="text-xs text-muted-foreground font-medium">
                    {testimonial.role}
                  </p>
                </div>
              </CardFooter>

              {/* Decorative element */}
              <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
