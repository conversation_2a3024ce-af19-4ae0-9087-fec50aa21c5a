import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"

export function CtaSection() {
  return (
    <section className="w-full py-12 md:py-24 bg-[#DEE086]/20">
      <div className="container px-4 md:px-6">
        <div className="flex flex-col items-center justify-center space-y-4 text-center">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Ready to Join Our Community?</h2>
            <p className="max-w-[700px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
              Take the first step towards a bright future at NES High School.
            </p>
          </div>
          <div className="flex flex-col gap-2 min-[400px]:flex-row">
            <Button asChild size="lg" className="bg-[#00237C] hover:bg-[#00237C]/90">
              <Link href="/admissions/apply">Apply Now</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link href="/contact/form">Schedule a Visit</Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}