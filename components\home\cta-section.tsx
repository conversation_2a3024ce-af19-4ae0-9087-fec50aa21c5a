import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, Phone, Mail, MapPin, Calendar } from "lucide-react"

export function CtaSection() {
  return (
    <className="relative w-full section-padding bg-gradient-to-br from-yellow-50 via-orange-50 to-yellow-100 dark:from-yellow-900/10 dark:via-orange-900/10 dark:to-yellow-900/10 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23f59e0b" fill-opacity="0.05"%3E%3Cpath d="M30 30c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20zm20 0c0-11-9-20-20-20s-20 9-20 20 9 20 20 20 20-9 20-20z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-30"></div>

      {/* Floating Elements */}
      <div className="absolute top-10 left-10 w-20 h-20 bg-orange-400/10 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-10 right-10 w-32 h-32 bg-yellow-400/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      <div className="absolute top-1/2 right-1/4 w-16 h-16 bg-orange-300/10 rounded-full blur-xl animate-pulse" style={{ animationDelay: '4s' }}></div>

      <div className="container relative z-10 px-4 md:px-6">
        <div className="mx-auto max-w-4xl">
          <div className="flex flex-col items-center justify-center space-y-8 text-center">
            <div className="space-y-6">
              <div className="inline-flex items-center rounded-full bg-orange-100 dark:bg-orange-900/20 px-4 py-2 text-sm font-medium text-orange-700 dark:text-orange-400">
                <Calendar className="mr-2 h-4 w-4" />
                Start Your Journey Today
              </div>
              <h2 className="text-3xl font-bold tracking-tight sm:text-4xl lg:text-5xl text-balance">
                Ready to Join Our
                <span className="bg-gradient-to-r from-orange-600 to-yellow-600 bg-clip-text text-transparent"> Community?</span>
              </h2>
              <p className="max-w-[700px] text-lg text-muted-foreground leading-relaxed">
                Take the first step towards a bright future at NES High School. Join thousands of successful alumni who started their journey here.
              </p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
              <Button
                asChild
                size="lg"
                className="group bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 px-8"
              >
                <Link href="/admissions/apply">
                  Apply Now
                  <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-200 group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="lg"
                className="group border-2 border-orange-300 text-orange-700 hover:bg-orange-50 dark:border-orange-600 dark:text-orange-400 dark:hover:bg-orange-900/20 transition-all duration-200 px-8"
              >
                <Link href="/contact/form">
                  Schedule a Visit
                  <Phone className="ml-2 h-4 w-4 transition-transform duration-200 group-hover:scale-110" />
                </Link>
              </Button>
            </div>

            {/* Quick Contact Info */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 pt-8 w-full max-w-2xl">
              <div className="flex items-center justify-center gap-3 p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <Phone className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">(*************</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Call us</div>
                </div>
              </div>

              <div className="flex items-center justify-center gap-3 p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
                  <Mail className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100"><EMAIL></div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Email us</div>
                </div>
              </div>

              <div className="flex items-center justify-center gap-3 p-4 rounded-lg bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm">
                <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                  <MapPin className="h-4 w-4 text-orange-600 dark:text-orange-400" />
                </div>
                <div className="text-left">
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">Visit Campus</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Schedule tour</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}