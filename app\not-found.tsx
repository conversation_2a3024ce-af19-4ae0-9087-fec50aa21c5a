import Link from 'next/link'
import { Search, Home, ArrowLeft, BookOpen, Users, GraduationCap } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-blue-50 dark:from-blue-950/20 dark:via-background dark:to-blue-950/20">
      <div className="container mx-auto px-4 py-16 sm:py-24">
        <div className="text-center mb-12">
          {/* Large 404 */}
          <div className="mb-8">
            <h1 className="text-8xl sm:text-9xl font-bold text-transparent bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text animate-fade-in">
              404
            </h1>
            <div className="h-1 w-24 bg-gradient-to-r from-blue-600 to-blue-800 mx-auto rounded-full"></div>
          </div>
          
          {/* Error message */}
          <div className="space-y-4 animate-slide-up">
            <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 dark:text-gray-100">
              Page Not Found
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
            </p>
          </div>
        </div>

        <div className="max-w-4xl mx-auto grid gap-8 md:grid-cols-2">
          {/* Search Section */}
          <Card className="animate-scale-in">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5 text-blue-600" />
                Search Our Site
              </CardTitle>
              <CardDescription>
                Try searching for what you're looking for
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex gap-2">
                <Input 
                  placeholder="Search for pages, content..." 
                  className="flex-1"
                />
                <Button>
                  <Search className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="animate-scale-in" style={{ animationDelay: '0.1s' }}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Home className="h-5 w-5 text-blue-600" />
                Quick Navigation
              </CardTitle>
              <CardDescription>
                Get back on track with these helpful links
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/">
                  <Home className="mr-2 h-4 w-4" />
                  Back to Homepage
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/about/school">
                  <BookOpen className="mr-2 h-4 w-4" />
                  About Our School
                </Link>
              </Button>
              <Button asChild variant="outline" className="w-full justify-start">
                <Link href="/admissions/apply">
                  <GraduationCap className="mr-2 h-4 w-4" />
                  Apply for Admission
                </Link>
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Popular Pages */}
        <div className="mt-16 animate-fade-in" style={{ animationDelay: '0.2s' }}>
          <h3 className="text-2xl font-bold text-center mb-8 text-gray-900 dark:text-gray-100">
            Popular Pages
          </h3>
          <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3 max-w-4xl mx-auto">
            <Link 
              href="/academics/curriculum" 
              className="group p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-md"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-blue-100 dark:bg-blue-900/20 group-hover:bg-blue-200 dark:group-hover:bg-blue-900/30 transition-colors">
                  <BookOpen className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Academics</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Curriculum & Programs</p>
                </div>
              </div>
            </Link>

            <Link 
              href="/campus-life/activities" 
              className="group p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-md"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-green-100 dark:bg-green-900/20 group-hover:bg-green-200 dark:group-hover:bg-green-900/30 transition-colors">
                  <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Campus Life</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Activities & Events</p>
                </div>
              </div>
            </Link>

            <Link 
              href="/contact/form" 
              className="group p-6 rounded-lg border border-gray-200 dark:border-gray-700 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-md"
            >
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-orange-100 dark:bg-orange-900/20 group-hover:bg-orange-200 dark:group-hover:bg-orange-900/30 transition-colors">
                  <Users className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100">Contact Us</h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Get in Touch</p>
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* Back Button */}
        <div className="text-center mt-12">
          <Button 
            onClick={() => window.history.back()} 
            variant="ghost" 
            className="animate-fade-in"
            style={{ animationDelay: '0.3s' }}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        </div>
      </div>
    </div>
  )
}
