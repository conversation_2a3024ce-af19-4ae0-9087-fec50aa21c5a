import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { ErrorBoundary } from "@/components/error-boundary";
import { Toaster } from "@/components/ui/sonner";
import { Toaster as ToastToaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "NES High School - Excellence in Education",
  description:
    "NES High School provides exceptional education with a focus on academic excellence, character development, and community engagement.",
  keywords:
    "high school, education, NES, academic excellence, college prep, extracurricular activities",
  authors: [{ name: "NES High School" }],
  creator: "NES High School",
  publisher: "NES High School",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://neshighschool.edu"),
  openGraph: {
    title: "NES High School - Excellence in Education",
    description:
      "NES High School provides exceptional education with a focus on academic excellence, character development, and community engagement.",
    url: "https://neshighschool.edu",
    siteName: "NES High School",
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "NES High School - Excellence in Education",
    description:
      "NES High School provides exceptional education with a focus on academic excellence, character development, and community engagement.",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ErrorBoundary>
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange
          >
            <div className="flex min-h-screen flex-col">
              <Navbar />
              <main className="flex-1">{children}</main>
              <Footer />
            </div>
            <Toaster />
            <ToastToaster />
          </ThemeProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
