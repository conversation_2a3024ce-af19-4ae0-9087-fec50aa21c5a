import Link from "next/link"
import { Button } from "@/components/ui/button"

export function HeroSection() {
  return (
    <section className="w-full py-12 md:py-24 lg:py-32 xl:py-48 bg-gradient-to-b from-[#00237C] to-[#004500] text-white">
      <div className="container px-4 md:px-6">
        <div className="grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]">
          <div className="flex flex-col justify-center space-y-4">
            <div className="space-y-2">
              <h1 className="text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none">
                Nurturing Excellence in Education Since 1975
              </h1>
              <p className="max-w-[600px] text-muted-foreground md:text-xl lg:text-base xl:text-xl">
                At NES High School, we provide a rigorous academic environment that prepares students for college and beyond, while fostering character development and community engagement.
              </p>
            </div>
            <div className="flex flex-col gap-2 min-[400px]:flex-row">
              <Button asChild size="lg" className="bg-[#E19321] hover:bg-[#E19321]/90">
                <Link href="/admissions/apply">Apply Now</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-[#00237C]">
                <Link href="/about/school">Learn More</Link>
              </Button>
            </div>
          </div>
          <div className="flex items-center justify-center">
            <div className="rounded-lg overflow-hidden border-8 border-white/10 shadow-xl">
              <img
                alt="NES High School campus"
                className="aspect-video object-cover w-full"
                height="375"
                src="https://images.pexels.com/photos/207692/pexels-photo-207692.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750"
                width="667"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}