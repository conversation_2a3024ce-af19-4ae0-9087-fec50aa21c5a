import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { GraduationCap, Users, BookOpen, Award } from "lucide-react"

export function HeroSection() {
  return (
    <section className="relative w-full min-h-screen flex items-center bg-gradient-to-br from-[#00237C] via-[#003366] to-[#004500] text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.03"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')] opacity-20"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-white/5 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-blue-400/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '2s' }}></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-yellow-400/10 rounded-full blur-xl animate-pulse" style={{ animationDelay: '4s' }}></div>

      <div className="container relative z-10 px-4 md:px-6 py-20">
        <div className="grid gap-12 lg:grid-cols-[1fr_500px] lg:gap-16 xl:grid-cols-[1fr_600px] items-center">
          <div className="flex flex-col justify-center space-y-8 animate-fade-in">
            <div className="space-y-6">
              <div className="inline-flex items-center rounded-full bg-white/10 backdrop-blur-sm px-4 py-2 text-sm font-medium">
                <Award className="mr-2 h-4 w-4" />
                Excellence in Education Since 1975
              </div>
              <h1 className="text-4xl font-bold tracking-tight sm:text-5xl xl:text-6xl/tight text-balance">
                Nurturing Tomorrow's
                <span className="bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent"> Leaders</span>
              </h1>
              <p className="max-w-[600px] text-lg text-white/90 leading-relaxed">
                At NES High School, we provide a rigorous academic environment that prepares students for college and beyond, while fostering character development and community engagement.
              </p>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 py-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-400">98%</div>
                <div className="text-sm text-white/80">College Acceptance</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">25+</div>
                <div className="text-sm text-white/80">AP Courses</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">150+</div>
                <div className="text-sm text-white/80">Faculty</div>
              </div>
            </div>

            <div className="flex flex-col gap-4 sm:flex-row">
              <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200">
                <Link href="/admissions/apply">Apply Now</Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="text-white border-white/30 bg-white/10 backdrop-blur-sm hover:bg-white hover:text-[#00237C] transition-all duration-200">
                <Link href="/about/school">Learn More</Link>
              </Button>
            </div>
          </div>

          <div className="flex items-center justify-center animate-slide-up" style={{ animationDelay: '0.3s' }}>
            <div className="relative">
              {/* Main Image */}
              <div className="relative rounded-2xl overflow-hidden border-4 border-white/20 shadow-2xl backdrop-blur-sm">
                <Image
                  src="/assets/images/logo.jpg"
                  alt="NES High School campus"
                  width={600}
                  height={400}
                  className="object-cover w-full h-[400px]"
                  priority
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
              </div>

              {/* Floating Cards */}
              <div className="absolute -top-6 -left-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg animate-scale-in" style={{ animationDelay: '0.6s' }}>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <GraduationCap className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Academic Excellence</div>
                    <div className="text-sm text-gray-600">Top-tier education</div>
                  </div>
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white/95 backdrop-blur-sm rounded-xl p-4 shadow-lg animate-scale-in" style={{ animationDelay: '0.9s' }}>
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Users className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">Community</div>
                    <div className="text-sm text-gray-600">Strong connections</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}