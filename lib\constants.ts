// Navigation items with their dropdown menus
export const navItems = [
  {
    title: "Home",
    href: "/",
    items: [],
  },
  {
    title: "About",
    href: "/about",
    items: [
      {
        title: "School",
        href: "/about/school",
        description: "Learn about our history, mission, and values",
      },
      {
        title: "Founder",
        href: "/about/founder",
        description: "Meet the visionary behind NES High School",
      },
      {
        title: "Principal",
        href: "/about/principal",
        description: "Message from our school leadership",
      },
    ],
  },
  {
    title: "Academics",
    href: "/academics",
    items: [
      {
        title: "Curriculum",
        href: "/academics/curriculum",
        description: "Explore our comprehensive academic programs",
      },
      {
        title: "Question Bank",
        href: "/academics/question-bank",
        description: "Access study resources and practice materials",
      },
    ],
  },
  {
    title: "Campus Life",
    href: "/campus-life",
    items: [
      {
        title: "Activities",
        href: "/campus-life/activities",
        description: "Discover extracurricular opportunities",
      },
      {
        title: "Facilities",
        href: "/campus-life/facilities",
        description: "Tour our state-of-the-art campus facilities",
      },
    ],
  },
  {
    title: "Admissions",
    href: "/admissions",
    items: [
      {
        title: "Online Form",
        href: "/admissions/apply",
        description: "Start your application process",
      },
      {
        title: "Fee Structure",
        href: "/admissions/fees",
        description: "View tuition and payment information",
      },
    ],
  },
  {
    title: "Alumni",
    href: "/alumni",
    items: [
      {
        title: "Directory",
        href: "/alumni/directory",
        description: "Connect with NES graduates",
      },
      {
        title: "Events",
        href: "/alumni/events",
        description: "Upcoming alumni gatherings and reunions",
      },
    ],
  },
  {
    title: "Contact",
    href: "/contact",
    items: [
      {
        title: "Location Map",
        href: "/contact/location",
        description: "Find us and plan your visit",
      },
      {
        title: "Contact Form",
        href: "/contact/form",
        description: "Reach out with questions or feedback",
      },
    ],
  },
]

// School colors based on the provided color scheme
export const schoolColors = {
  primary: {
    navy: "#00237C",
    green: "#004500",
    charcoal: "#585858",
  },
  accent: {
    lightBlue: "#51A5FE",
    yellow: "#DEE086",
    orange: "#E19321",
  },
}

// School stats for the homepage
export const schoolStats = [
  { value: "1975", label: "Established" },
  { value: "98%", label: "College Acceptance" },
  { value: "25+", label: "AP Courses" },
  { value: "150+", label: "Faculty Members" },
]

// Testimonials for the homepage
export const testimonials = [
  {
    quote: "NES High School provided me with the foundation I needed to succeed in college and beyond. The teachers are dedicated and the curriculum is challenging in all the right ways.",
    author: "Sarah Johnson",
    role: "Class of 2022, Stanford University",
    avatar: "https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=150",
  },
  {
    quote: "The opportunities at NES are unparalleled. From robotics to debate club, I was able to explore my interests and develop skills that set me apart in my college applications.",
    author: "Michael Chen",
    role: "Class of 2023, MIT",
    avatar: "https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg?auto=compress&cs=tinysrgb&w=150",
  },
  {
    quote: "My years at NES were transformative. The school's emphasis on character development alongside academic excellence has shaped me into the person I am today.",
    author: "Priya Patel",
    role: "Class of 2021, Yale University",
    avatar: "https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=150",
  },
]

// Upcoming events for the calendar
export const upcomingEvents = [
  {
    id: 1,
    title: "Parent-Teacher Conference",
    date: "2025-05-20",
    time: "4:00 PM - 8:00 PM",
    location: "Main Building",
  },
  {
    id: 2,
    title: "Annual Sports Day",
    date: "2025-06-05",
    time: "9:00 AM - 4:00 PM",
    location: "Sports Complex",
  },
  {
    id: 3,
    title: "Graduation Ceremony",
    date: "2025-06-15",
    time: "11:00 AM - 1:00 PM",
    location: "Auditorium",
  },
  {
    id: 4,
    title: "Summer School Registration",
    date: "2025-06-20",
    time: "10:00 AM - 3:00 PM",
    location: "Administration Office",
  },
  {
    id: 5,
    title: "Alumni Reunion",
    date: "2025-07-10",
    time: "6:00 PM - 9:00 PM",
    location: "School Gymnasium",
  },
]