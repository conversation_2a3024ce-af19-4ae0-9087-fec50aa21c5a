import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { ArrowRight } from "lucide-react"

interface NewsCardProps {
  date: string
  title: string
  description: string
  category: string
}

export function NewsCard({ date, title, description, category }: NewsCardProps) {
  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <p className="text-sm text-muted-foreground">{date}</p>
          <Badge variant="secondary">{category}</Badge>
        </div>
        <CardTitle className="line-clamp-2 text-xl">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="line-clamp-3 text-base">{description}</CardDescription>
      </CardContent>
      <CardFooter>
        <Link href="#" className="text-sm inline-flex items-center font-medium text-primary hover:underline">
          Read more <ArrowRight className="ml-1 h-3 w-3" />
        </Link>
      </CardFooter>
    </Card>
  )
}