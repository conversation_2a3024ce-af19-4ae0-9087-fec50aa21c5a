import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { ArrowRight, Calendar, Clock } from "lucide-react";

interface NewsCardProps {
  date: string;
  title: string;
  description: string;
  category: string;
}

const categoryColors = {
  Academic: "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400",
  Events:
    "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400",
  Guidance:
    "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400",
  Sports:
    "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400",
  default: "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400",
};

export function NewsCard({
  date,
  title,
  description,
  category,
}: NewsCardProps) {
  const categoryColor =
    categoryColors[category as keyof typeof categoryColors] ||
    categoryColors.default;

  return (
    <Card className="group overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 border-0 bg-gradient-to-br from-white to-gray-50/50 dark:from-gray-900 dark:to-gray-800/50">
      {/* Gradient overlay on hover */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <CardHeader className="pb-3 relative z-10">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Calendar className="h-4 w-4" />
            <span>{date}</span>
          </div>
          <Badge className={`${categoryColor} border-0 font-medium`}>
            {category}
          </Badge>
        </div>
        <CardTitle className="line-clamp-2 text-xl font-bold group-hover:text-primary transition-colors duration-200 leading-tight">
          {title}
        </CardTitle>
      </CardHeader>

      <CardContent className="pb-4 relative z-10">
        <CardDescription className="line-clamp-3 text-base leading-relaxed text-muted-foreground group-hover:text-foreground/80 transition-colors duration-200">
          {description}
        </CardDescription>
      </CardContent>

      <CardFooter className="pt-0 relative z-10">
        <Link
          href="#"
          className="inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/80 transition-colors duration-200 group/link"
        >
          <span>Read more</span>
          <ArrowRight className="h-3 w-3 transition-transform duration-200 group-hover/link:translate-x-1" />
        </Link>
      </CardFooter>

      {/* Decorative element */}
      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
    </Card>
  );
}
